<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
        then: function () {
            // Web routes with api/v1 prefix (these are API routes using web guard)
            Route::middleware('web')
                ->prefix('api/v1')
                ->group(base_path('routes/web.php'));

            // API routes with api/v1 prefix
            Route::middleware('api')
                ->prefix('api/v1')
                ->group(base_path('routes/api.php'));

            // Central domain routes (for customers, public APIs)
            Route::middleware('api')
                ->prefix('api/v1')
                ->group(base_path('routes/central.php'));

            // Admin routes (for platform administrators)
            Route::middleware(['api', 'auth:sanctum', 'role:platform-admin|super-admin'])
                ->prefix('api/v1/admin')
                ->group(base_path('routes/admin.php'));
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Global middleware
        $middleware->append(App\Http\Middleware\RequestLoggingMiddleware::class);
        $middleware->append(App\Http\Middleware\SecurityHeaders::class);
        // $middleware->append(App\Http\Middleware\SecurityMonitoring::class);
        $middleware->append(App\Http\Middleware\DDoSProtection::class);
        $middleware->append(App\Http\Middleware\TrackTokenUsage::class);

        // Alias middleware
        $middleware->alias([
            'auth.rate_limit' => App\Http\Middleware\AuthRateLimitMiddleware::class,
            'tenant.rate_limit' => App\Http\Middleware\TenantAwareRateLimit::class,
            'api.performance' => App\Http\Middleware\ApiPerformanceMonitoring::class,
            'tenant' => App\Http\Middleware\TenantAccessMiddleware::class,
            'role' => App\Http\Middleware\RoleMiddleware::class,
            'subscription.feature' => App\Http\Middleware\SubscriptionFeatureGate::class,
        ]);
    })
    ->withProviders([
        App\Providers\MonitoringServiceProvider::class,
    ])
    ->withExceptions(function (Exceptions $exceptions) {
        // Log all exceptions (skip database logging during testing)
        $exceptions->reportable(function (Throwable $e) {
            if (! app()->environment('testing')) {
                app(App\Services\System\LoggingService::class)->logException($e);
            }
        });

        // Handle custom exceptions
        $exceptions->render(function (App\Exceptions\AuthenticationException $e) {
            return $e->render();
        });

        $exceptions->render(function (App\Exceptions\TenantException $e) {
            return $e->render();
        });

        $exceptions->render(function (App\Exceptions\ValidationException $e) {
            return $e->render();
        });

        $exceptions->render(function (App\Exceptions\BusinessLogicException $e) {
            return $e->render();
        });

        $exceptions->render(function (App\Exceptions\ExternalServiceException $e) {
            return $e->render();
        });

        $exceptions->render(function (App\Exceptions\RateLimitException $e) {
            return $e->render();
        });

        $exceptions->render(function (App\Exceptions\PaymentException $e) {
            return $e->render();
        });

        // Handle Laravel validation exceptions
        $exceptions->render(function (Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
                'error_code' => 'VALIDATION_ERROR',
                'timestamp' => now()->toISOString(),
            ], 422);
        });

        // Handle authentication exceptions
        $exceptions->render(function (Illuminate\Auth\AuthenticationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated',
                'error_code' => 'UNAUTHENTICATED',
                'timestamp' => now()->toISOString(),
            ], 401);
        });

        // Handle authorization exceptions
        $exceptions->render(function (Illuminate\Auth\Access\AuthorizationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Forbidden',
                'error_code' => 'FORBIDDEN',
                'timestamp' => now()->toISOString(),
            ], 403);
        });

        // Handle model not found exceptions
        $exceptions->render(function (Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Resource not found',
                'error_code' => 'NOT_FOUND',
                'timestamp' => now()->toISOString(),
            ], 404);
        });

        // Handle database exceptions
        $exceptions->render(function (Illuminate\Database\QueryException $e) {
            $message = app()->environment('production')
                ? 'Database error occurred'
                : $e->getMessage();

            return response()->json([
                'success' => false,
                'message' => $message,
                'error_code' => 'DATABASE_ERROR',
                'timestamp' => now()->toISOString(),
            ], 500);
        });

        // Handle HTTP exceptions
        $exceptions->render(function (Symfony\Component\HttpKernel\Exception\HttpException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage() ?: 'HTTP error occurred',
                'error_code' => 'HTTP_ERROR',
                'timestamp' => now()->toISOString(),
            ], $e->getStatusCode());
        });

        // Handle throttle exceptions
        $exceptions->render(function (Illuminate\Http\Exceptions\ThrottleRequestsException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Too many requests',
                'error_code' => 'RATE_LIMIT_EXCEEDED',
                'retry_after' => $e->getHeaders()['Retry-After'] ?? 60,
                'timestamp' => now()->toISOString(),
            ], 429)->withHeaders($e->getHeaders());
        });

        // Handle general exceptions
        $exceptions->render(function (Throwable $e) {
            $message = app()->environment('production')
                ? 'An unexpected error occurred'
                : $e->getMessage();

            return response()->json([
                'success' => false,
                'message' => $message,
                'error_code' => 'INTERNAL_ERROR',
                'timestamp' => now()->toISOString(),
            ], 500);
        });
    })->create();
