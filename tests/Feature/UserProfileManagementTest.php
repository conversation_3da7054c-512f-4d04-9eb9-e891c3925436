<?php

declare(strict_types=1);

use App\Models\User\User;
use App\Models\User\UserPreference;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

// Remove the beforeEach hook as RefreshDatabase handles this

test('user can get their profile', function () {
    // Create a central user (no tenant_id) for testing central routes
    $user = User::factory()->create(['tenant_id' => null]);
    Sanctum::actingAs($user);

    $response = $this->getJson('/api/v1/user/profile');

    // Debug the response
    if ($response->status() !== 200) {
        $this->fail('Expected 200, got ' . $response->status() . '. Response: ' . $response->content());
    }

    $response->assertOk()
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'name',
                'email',
                'phone_number',
                'timezone',
                'is_active',
                'user_type',
                'verification',
                'profile',
                'last_login_at',
                'created_at',
                'updated_at',
            ],
        ]);
});

test('user can update their profile', function () {
    $user = User::factory()->create(['tenant_id' => null]);
    Sanctum::actingAs($user);

    $updateData = [
        'first_name' => 'Updated',
        'last_name' => 'Name',
        'timezone' => 'Africa/Lagos',
        'bio' => 'This is my updated bio',
        'gender' => 'male',
    ];

    $response = $this->putJson('/api/v1/user/profile', $updateData);

    $response->assertOk()
        ->assertJsonPath('data.first_name', 'Updated')
        ->assertJsonPath('data.last_name', 'Name')
        ->assertJsonPath('data.name', 'Updated Name') // Computed full name
        ->assertJsonPath('data.timezone', 'Africa/Lagos')
        ->assertJsonPath('data.profile.bio', 'This is my updated bio')
        ->assertJsonPath('data.profile.gender', 'male');

    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'first_name' => 'Updated',
        'last_name' => 'Name',
        'timezone' => 'Africa/Lagos',
    ]);

    $this->assertDatabaseHas('customer_profiles', [
        'user_id' => $user->id,
        'bio' => 'This is my updated bio',
        'gender' => 'male',
    ]);
});

test('user can change their password', function () {
    $user = User::factory()->create([
        'tenant_id' => null,
        'password' => bcrypt('oldpassword123'),
    ]);
    Sanctum::actingAs($user);

    $response = $this->postJson('/api/v1/user/change-password', [
        'current_password' => 'oldpassword123',
        'new_password' => 'MyVerySecureNewPassword2024!@#',
        'new_password_confirmation' => 'MyVerySecureNewPassword2024!@#',
    ]);

    $response->assertOk()
        ->assertJsonPath('message', 'Password changed successfully');

    // Verify old password no longer works
    $this->assertFalse(Hash::check('oldpassword123', $user->fresh()->password));

    // Verify new password works
    $this->assertTrue(Hash::check('MyVerySecureNewPassword2024!@#', $user->fresh()->password));
});

test('user can get their preferences', function () {
    $user = User::factory()->create(['tenant_id' => null]);
    Sanctum::actingAs($user);

    // Create some preferences
    UserPreference::create([
        'user_id' => $user->id,
        'tenant_id' => $user->tenant_id,
        'preference_key' => 'app.theme',
        'preference_value' => 'dark',
    ]);

    UserPreference::create([
        'user_id' => $user->id,
        'tenant_id' => $user->tenant_id,
        'preference_key' => 'notifications.email_enabled',
        'preference_value' => 'true',
    ]);

    $response = $this->getJson('/api/v1/user/preferences');

    $response->assertOk()
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'notifications',
                'communication',
                'privacy',
                'app',
            ],
        ]);
});

test('user can update their preferences', function () {
    $user = User::factory()->create(['tenant_id' => null]);
    Sanctum::actingAs($user);

    $preferences = [
        'app' => [
            'theme' => 'dark',
            'currency' => 'NGN',
        ],
        'notifications' => [
            'email_enabled' => true,
            'sms_enabled' => false,
        ],
        'privacy' => [
            'location_sharing' => false,
        ],
    ];

    $response = $this->putJson('/api/v1/user/preferences', $preferences);

    $response->assertOk()
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'notifications',
                'communication',
                'privacy',
                'app',
            ],
        ]);

    // Verify preferences were saved
    $this->assertDatabaseHas('user_preferences', [
        'user_id' => $user->id,
        'preference_key' => 'app.theme',
        'preference_value' => 'dark',
    ]);

    $this->assertDatabaseHas('user_preferences', [
        'user_id' => $user->id,
        'preference_key' => 'notifications.email_enabled',
        'preference_value' => 'true',
    ]);
});

test('user can get their active devices', function () {
    $user = User::factory()->create(['tenant_id' => null]);
    $user->createToken('Test Device'); // Create a device token for testing
    Sanctum::actingAs($user);

    $response = $this->getJson('/api/v1/user/devices');

    $response->assertOk()
        ->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'is_current',
                    'platform',
                    'browser',
                    'device_type',
                    'last_used_at',
                    'created_at',
                ],
            ],
        ]);
});

test('user can deactivate their account', function () {
    $user = User::factory()->create([
        'tenant_id' => null,
        'password' => bcrypt('password123'),
    ]);
    Sanctum::actingAs($user);

    $response = $this->postJson('/api/v1/user/deactivate', [
        'password' => 'password123',
        'reason' => 'No longer needed',
    ]);

    $response->assertOk()
        ->assertJsonPath('message', 'Central user account deactivated successfully');

    $this->assertDatabaseHas('users', [
        'id' => $user->id,
        'is_active' => false,
    ]);

    // Verify all tokens were revoked
    $this->assertEquals(0, $user->tokens()->count());
});

test('validation works for profile updates', function () {
    $user = User::factory()->create(['tenant_id' => null]);
    Sanctum::actingAs($user);

    $response = $this->putJson('/api/v1/user/profile', [
        'email' => 'invalid-email',
        'phone_number' => 'invalid-phone',
        'date_of_birth' => 'future-date',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['email', 'phone_number', 'date_of_birth']);
});

test('validation works for password change', function () {
    $user = User::factory()->create(['tenant_id' => null]);
    Sanctum::actingAs($user);

    $response = $this->postJson('/api/v1/user/change-password', [
        'current_password' => 'wrong-password',
        'new_password' => 'weak',
        'new_password_confirmation' => 'different',
    ]);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['new_password']);
});
