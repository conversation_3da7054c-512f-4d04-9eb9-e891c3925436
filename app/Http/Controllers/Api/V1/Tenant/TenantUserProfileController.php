<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\ChangePasswordRequest;
use App\Http\Requests\Api\V1\DeactivateAccountRequest;
use App\Http\Requests\Api\V1\UpdateProfileRequest;
use App\Http\Requests\Api\V1\UpdateUserPreferencesRequest;
use App\Services\User\UserProfileService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Tenant User Profile Controller
 * 
 * Handles user profile operations for tenant domain users.
 * This includes business owners, staff, delivery providers, and their teams.
 */
class TenantUserProfileController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly UserProfileService $userProfileService
    ) {}

    /**
     * Get user profile.
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Validate tenant access
        if (!$this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }
        
        $profileResource = $this->userProfileService->getProfile($user, 'tenant');
        
        return $this->successResponse(
            'Tenant user profile retrieved successfully',
            $profileResource
        );
    }

    /**
     * Update user profile.
     */
    public function update(UpdateProfileRequest $request): JsonResponse
    {
        $user = $request->user();
        
        // Validate tenant access
        if (!$this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }
        
        $data = $request->validated();

        $updatedUser = $this->userProfileService->updateProfile($user, $data);
        $profileResource = $this->userProfileService->getProfile($updatedUser, 'tenant');

        return $this->successResponse(
            'Tenant user profile updated successfully',
            $profileResource
        );
    }

    /**
     * Change user password.
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $user = $request->user();
        
        // Validate tenant access
        if (!$this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }
        
        $data = $request->validated();

        $this->userProfileService->changePassword(
            $user,
            $data['current_password'],
            $data['new_password']
        );

        return $this->successResponse('Password changed successfully');
    }

    /**
     * Get user preferences.
     */
    public function getPreferences(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Validate tenant access
        if (!$this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }
        
        $preferences = $this->userProfileService->getUserPreferences($user);

        return $this->successResponse(
            'Tenant user preferences retrieved successfully',
            $preferences
        );
    }

    /**
     * Update user preferences.
     */
    public function updatePreferences(UpdateUserPreferencesRequest $request): JsonResponse
    {
        $user = $request->user();
        
        // Validate tenant access
        if (!$this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }
        
        $preferences = $request->validated();

        $updatedPreferences = $this->userProfileService->updateUserPreferences($user, $preferences);

        return $this->successResponse(
            'Tenant user preferences updated successfully',
            $updatedPreferences
        );
    }

    /**
     * Deactivate user account.
     */
    public function deactivate(DeactivateAccountRequest $request): JsonResponse
    {
        $user = $request->user();
        
        // Validate tenant access
        if (!$this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }
        
        $data = $request->validated();

        $this->userProfileService->deactivateAccount(
            $user,
            $data['password'],
            $data['reason'] ?? null
        );

        return $this->successResponse('Tenant user account deactivated successfully');
    }

    /**
     * Validate that the user has access to the current tenant.
     */
    private function validateTenantAccess($user): bool
    {
        $tenant = tenant();
        
        if (!$tenant) {
            return false;
        }
        
        // Check if user belongs to this tenant
        return $user->tenant_id === $tenant->id;
    }
}
