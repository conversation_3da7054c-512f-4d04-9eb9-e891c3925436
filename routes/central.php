<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\Auth\ApiAuthController;
use App\Http\Controllers\Api\V1\Auth\OAuthController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerBusinessSearchController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerCategorySearchController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerBusinessBrowsingController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerDeliveryController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerDeliveryProviderSearchController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerOrderController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerOrderSearchController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerOrderTrackingController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerPaymentController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerPickupController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerProductReviewController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerProductSearchController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerProviderBrowsingController;
use App\Http\Controllers\Api\V1\Central\Customer\CustomerUnifiedSearchController;
use App\Http\Controllers\Api\V1\Central\HomepageShowcaseController;
use App\Http\Controllers\Api\V1\Shared\GoogleMapsController;
use App\Http\Controllers\Api\V1\Shared\ChatController;
use App\Http\Controllers\Api\V1\Shared\DeviceController;
use App\Http\Controllers\Api\V1\Shared\NotificationController;
use App\Http\Controllers\Api\V1\Central\CentralUserProfileController;
use App\Http\Controllers\Api\V1\Shared\UnifiedKycController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Central Domain API Routes
|--------------------------------------------------------------------------
|
| These routes are for the central domain (api.deliverynexus.com)
| Used by: Customers, Platform Admins, Public APIs
| No tenant context - can access cross-tenant data
|
*/

// Authentication routes (no authentication required)
Route::prefix('auth')->middleware(['auth.rate_limit:5,15'])->group(function () {
    // Central domain login (customers, platform admins, and tenant owners)
    Route::post('/login', [ApiAuthController::class, 'centralLogin']);
    Route::post('/password/reset', [ApiAuthController::class, 'sendPasswordReset']);
    Route::post('/password/confirm', [ApiAuthController::class, 'resetPassword']);

    // Mobile app helper endpoint
    Route::post('/user-context', [ApiAuthController::class, 'getUserContext']);

    // Progressive registration flow (central domain only)
    Route::post('/register', [ApiAuthController::class, 'register']); // Phase 1: Simple email + password
    Route::post('/select-account-type', [ApiAuthController::class, 'selectAccountType'])->middleware('auth:sanctum'); // Phase 2: Choose account type
    Route::post('/onboard/business', [ApiAuthController::class, 'onboardBusiness'])->middleware('auth:sanctum'); // Phase 3: Business onboarding
    Route::post('/onboard/provider', [ApiAuthController::class, 'onboardProvider'])->middleware('auth:sanctum'); // Phase 3: Provider onboarding
    Route::post('/register/invitation', [ApiAuthController::class, 'registerFromInvitation']); // Staff invitation signup

    // OAuth authentication routes
    Route::prefix('oauth')->group(function () {
        Route::get('/providers', [OAuthController::class, 'getSupportedProviders']);
        Route::get('/auth-url', [OAuthController::class, 'getAuthUrl']);
        Route::get('/callback/{provider}', [OAuthController::class, 'handleCallback']);
        Route::post('/token', [OAuthController::class, 'authenticateWithToken']);

        // Convenience routes for provider-specific authentication
        Route::post('/google', [OAuthController::class, 'authenticateWithGoogle']);
        Route::post('/apple', [OAuthController::class, 'authenticateWithApple']);
    });
});

// Homepage showcase APIs (everyone can access)
Route::prefix('showcase')->group(function () {
    Route::get('/featured-businesses', [HomepageShowcaseController::class, 'featuredBusinesses']);
    Route::get('/featured-providers', [HomepageShowcaseController::class, 'featuredProviders']);
    Route::get('/statistics', [HomepageShowcaseController::class, 'statistics']);
    Route::get('/testimonials', [HomepageShowcaseController::class, 'testimonials']);
    Route::get('/pricing', [HomepageShowcaseController::class, 'pricing']);
    Route::get('/', [HomepageShowcaseController::class, 'showcase']); // All-in-one endpoint
});

// Protected routes (require authentication)
Route::middleware(['auth:sanctum'])->group(function () {
    // Authentication routes (authentication required)
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [ApiAuthController::class, 'logout']);
        Route::post('/logout-all', [ApiAuthController::class, 'logoutFromAllDevices']);
        Route::post('/refresh', [ApiAuthController::class, 'refreshToken']);
        Route::get('/me', [ApiAuthController::class, 'me']);

        // Verification routes
        Route::middleware(['auth.rate_limit:3,1'])->group(function () {
            Route::post('/email/send-verification', [ApiAuthController::class, 'sendEmailVerification']);
            Route::post('/phone/send-verification', [ApiAuthController::class, 'sendPhoneVerification']);
        });
        Route::middleware(['auth.rate_limit:5,1'])->group(function () {
            Route::post('/email/verify', [ApiAuthController::class, 'verifyEmail']);
            Route::post('/phone/verify', [ApiAuthController::class, 'verifyPhone']);
        });

        // Tenant access routes
        Route::get('/tenants', [ApiAuthController::class, 'getAccessibleTenants']);
        Route::get('/tenants/{tenantId}/access', [ApiAuthController::class, 'checkTenantAccess']);

        // OAuth management routes
        Route::prefix('oauth')->group(function () {
            Route::get('/user-info', [OAuthController::class, 'getUserInfo']);
            Route::post('/revoke', [OAuthController::class, 'revokeToken']);
        });
    });

    // User Profile Management routes (central controller)
    Route::middleware('central')->prefix('user')->group(function () {
        Route::get('/profile', [CentralUserProfileController::class, 'show']);
        Route::put('/profile', [CentralUserProfileController::class, 'update']);
        Route::post('/change-password', [CentralUserProfileController::class, 'changePassword']);
        Route::get('/preferences', [CentralUserProfileController::class, 'getPreferences']);
        Route::put('/preferences', [CentralUserProfileController::class, 'updatePreferences']);
        Route::get('/devices', [DeviceController::class, 'index']);
        Route::post('/deactivate', [CentralUserProfileController::class, 'deactivate']);
    });

    // Notification Management routes (shared across all user types)
    Route::prefix('notifications')->group(function () {
        Route::get('/', [NotificationController::class, 'index']);
        Route::get('/stats', [NotificationController::class, 'stats']);
        Route::get('/{id}', [NotificationController::class, 'show']);
        Route::put('/{id}/read', [NotificationController::class, 'markAsRead']);
        Route::put('/mark-all-read', [NotificationController::class, 'markAllAsRead']);
        Route::delete('/{id}', [NotificationController::class, 'destroy']);

        // FCM token management for push notifications
        Route::post('/fcm-tokens', [NotificationController::class, 'registerFcmToken']);
        Route::get('/fcm-tokens', [NotificationController::class, 'getFcmTokens']);
        Route::delete('/fcm-tokens', [NotificationController::class, 'removeFcmToken']);

        // Notification preferences
        Route::get('/preferences', [NotificationController::class, 'getPreferences']);
        Route::put('/preferences', [NotificationController::class, 'updatePreferences']);
        Route::post('/preferences/reset', [NotificationController::class, 'resetPreferences']);
    });

    // Chat system (shared across all user types)
    Route::prefix('chat')->group(function () {
        Route::post('/messages', [ChatController::class, 'sendMessage']);
        Route::get('/conversations/{conversation_id}/messages', [ChatController::class, 'getMessages']);
        Route::get('/conversations', [ChatController::class, 'getConversations']);
        Route::post('/conversations', [ChatController::class, 'createConversation']);
    });

    // Google Maps API routes (shared across all user types)
    Route::prefix('maps')->group(function () {
        Route::post('/geocode', [GoogleMapsController::class, 'geocodeAddress']);
        Route::post('/reverse-geocode', [GoogleMapsController::class, 'reverseGeocode']);
        Route::post('/distance', [GoogleMapsController::class, 'calculateDistance']);
        Route::post('/route', [GoogleMapsController::class, 'getOptimizedRoute']);
        Route::post('/validate-address', [GoogleMapsController::class, 'validateAddress']);
        Route::post('/delivery-eta', [GoogleMapsController::class, 'calculateDeliveryETA']);
        Route::get('/test-connection', [GoogleMapsController::class, 'testConnection']);
    });

    // Unified KYC routes (all authenticated users)
    Route::prefix('kyc')->group(function () {
        Route::post('/verify', [UnifiedKycController::class, 'initiateVerification']);
        Route::post('/verify-component', [UnifiedKycController::class, 'verifyComponent']);
        Route::get('/status', [UnifiedKycController::class, 'getStatus']);
        Route::get('/report', [UnifiedKycController::class, 'generateReport']);
        Route::get('/methods', [UnifiedKycController::class, 'getVerificationMethods']);
        Route::get('/banks', [UnifiedKycController::class, 'getSupportedBanks']);
        Route::get('/pricing', [UnifiedKycController::class, 'getPricing']);
        Route::get('/statistics', [UnifiedKycController::class, 'getStatistics']);
    });

    // Platform admin registration (super admin only)
    Route::post('/auth/register/admin', [ApiAuthController::class, 'registerAdmin'])
        ->middleware(['auth:sanctum', 'role:super-admin']);












    // Customer routes (customers only)
    Route::prefix('customer')->group(function () {
        // Search functionality (cross-tenant)
        Route::prefix('search')->group(function () {
            // Unified multi-domain search
            Route::post('/', [CustomerUnifiedSearchController::class, 'search']);
            Route::get('/suggestions', [CustomerUnifiedSearchController::class, 'suggestions']);
            Route::get('/trending', [CustomerUnifiedSearchController::class, 'trending']);

            // Product search
            Route::post('/products', [CustomerProductSearchController::class, 'search']);
            Route::get('/products/suggestions', [CustomerProductSearchController::class, 'suggestions']);
            Route::get('/products/popular-terms', [CustomerProductSearchController::class, 'popularTerms']);
            Route::get('/products/trending', [CustomerProductSearchController::class, 'trending']);
            Route::get('/products/filters', [CustomerProductSearchController::class, 'filters']);

            // Business search
            Route::post('/businesses', [CustomerBusinessSearchController::class, 'search']);
            Route::get('/businesses/suggestions', [CustomerBusinessSearchController::class, 'suggestions']);
            Route::get('/businesses/trending', [CustomerBusinessSearchController::class, 'trending']);

            // Delivery provider search
            Route::post('/delivery-providers', [CustomerDeliveryProviderSearchController::class, 'search']);
            Route::get('/delivery-providers/suggestions', [CustomerDeliveryProviderSearchController::class, 'suggestions']);
            Route::get('/delivery-providers/top-rated', [CustomerDeliveryProviderSearchController::class, 'topRated']);

            // Order search (customer's own orders)
            Route::post('/orders', [CustomerOrderSearchController::class, 'searchMyOrders']);
            Route::get('/orders/suggestions', [CustomerOrderSearchController::class, 'suggestions']);

            // Category search
            Route::post('/categories', [CustomerCategorySearchController::class, 'search']);
            Route::post('/categories/business/{businessId}', [CustomerCategorySearchController::class, 'searchByBusiness']);
            Route::get('/categories/suggestions', [CustomerCategorySearchController::class, 'suggestions']);
            Route::get('/categories/popular', [CustomerCategorySearchController::class, 'popular']);
        });

        // Business discovery (cross-tenant)
        Route::get('/businesses', [CustomerBusinessBrowsingController::class, 'index']);
        Route::get('/businesses/{id}', [CustomerBusinessBrowsingController::class, 'show']);
        Route::get('/businesses/{id}/products', [CustomerBusinessBrowsingController::class, 'products']);
        Route::get('/businesses/{id}/menu', [CustomerBusinessBrowsingController::class, 'menu']);

        // Provider discovery (cross-tenant)
        Route::get('/providers', [CustomerProviderBrowsingController::class, 'index']);
        Route::get('/providers/{id}', [CustomerProviderBrowsingController::class, 'show']);
        Route::get('/providers/{id}/service-areas', [CustomerProviderBrowsingController::class, 'serviceAreas']);
        Route::post('/providers/{id}/check-availability', [CustomerProviderBrowsingController::class, 'checkServiceAvailability']);

        // Order management (cross-tenant)
        Route::apiResource('orders', CustomerOrderController::class)->except(['update', 'destroy']);
        Route::post('orders/{order}/cancel', [CustomerOrderController::class, 'cancel']);
        Route::post('orders/{order}/rate', [CustomerOrderController::class, 'rate']);
        Route::get('orders/{order}/track', [CustomerOrderController::class, 'track']);

        // Payment management (cross-tenant)
        Route::post('orders/{order}/payment/initialize', [CustomerPaymentController::class, 'initializePayment']);
        Route::post('payments/verify', [CustomerPaymentController::class, 'verifyPayment']);
        Route::get('orders/{order}/payment-methods', [CustomerPaymentController::class, 'getPaymentMethods']);
        Route::get('payments/history', [CustomerPaymentController::class, 'getPaymentHistory']);

        // Product review management (cross-tenant)
        Route::get('products/{product}/reviews', [CustomerProductReviewController::class, 'index']);
        Route::post('products/{product}/reviews', [CustomerProductReviewController::class, 'store']);
        Route::put('reviews/{review}', [CustomerProductReviewController::class, 'update']);
        Route::delete('reviews/{review}', [CustomerProductReviewController::class, 'destroy']);
        Route::get('my-reviews', [CustomerProductReviewController::class, 'myReviews']);

        // Delivery requests (cross-tenant ad hoc deliveries)
        Route::apiResource('deliveries', CustomerDeliveryController::class)->except(['update', 'destroy']);
        Route::post('deliveries/request', [CustomerDeliveryController::class, 'request']);
        Route::post('deliveries/{delivery}/cancel', [CustomerDeliveryController::class, 'cancel']);
        Route::get('deliveries/{delivery}/track', [CustomerDeliveryController::class, 'track']);
        Route::post('deliveries/{delivery}/rate', [CustomerDeliveryController::class, 'rate']);

        // Pickup management (cross-tenant)
        Route::prefix('pickup')->group(function () {
            Route::get('/available-slots', [CustomerPickupController::class, 'getAvailableSlots']);
            Route::post('/book-slot', [CustomerPickupController::class, 'bookPickupSlot']);
            Route::delete('/orders/{order}/cancel-slot', [CustomerPickupController::class, 'cancelPickupSlot']);
            Route::get('/orders', [CustomerPickupController::class, 'getPickupOrders']);
        });

        // Real-time order tracking
        Route::prefix('tracking')->group(function () {
            Route::get('/orders/{order_id}', [CustomerOrderTrackingController::class, 'trackOrder']);
            Route::get('/orders/{order_id}/timeline', [CustomerOrderTrackingController::class, 'getOrderTimeline']);
            Route::get('/active-orders', [CustomerOrderTrackingController::class, 'getActiveOrders']);
        });

        // Customer profile (handled by CentralUserProfileController)
        // Customer-specific profile routes removed - use /api/v1/user/profile instead
    });

});
